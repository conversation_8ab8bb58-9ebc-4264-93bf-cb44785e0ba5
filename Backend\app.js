import dotenv from 'dotenv';
dotenv.config();
import express, { urlencoded } from 'express';
import connect from './db/db.js';
import cors from 'cors';
import cookieParser from 'cookie-parser';
connect();


const app=express();
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({extended:true}));
app.use(cookieParser());

app.get('/', (_req, res) => {
    res.send('Hello From home');
});

export default app;



