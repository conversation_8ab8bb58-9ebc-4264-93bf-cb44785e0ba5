import dotenv from 'dotenv';
dotenv.config();
import http from 'http';
import app from './app.js';
// import { Server } from 'socket.io';
import jwt from 'jsonwebtoken';
import mongoose from 'mongoose';

mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true });

const server = http.createServer(app);

const port = process.env.PORT || 3000

server.listen(port, () => {
    console.log(`Server is running on port ${port}`);
});

